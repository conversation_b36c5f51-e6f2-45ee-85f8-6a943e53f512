<?php

namespace Xmetr\Language\Traits;

use Xmetr\Language\Facades\Language;
use Xmetr\Language\LanguageManager;

trait TranslatedRouteCommandContext
{
    protected function isSupportedLocale(?string $locale): bool
    {
        return in_array($locale, $this->getSupportedLocales());
    }

    protected function getSupportedLocales(): array
    {
        return $this->getLocalization()->getSupportedLanguagesKeys();
    }

    protected function getLocalization()
    {
        return app(LanguageManager::class);
    }

    protected function getBootstrapPath(): string
    {
        return $this->laravel->bootstrapPath();
    }

    protected function makeLocaleRoutesPath(?string $locale = ''): string
    {
        $path = $this->laravel->getCachedRoutesPath();

        if (! $locale || (Language::hideDefaultLocaleInURL() && $locale == Language::getDefaultLocale())) {
            return $path;
        }

        $localePath = substr($path, 0, -4) . '_' . $locale . '.php';

        // Prevent path duplication by checking if the path already contains the base path twice
        $basePath = $this->laravel->basePath();
        $duplicatedBasePath = $basePath . DIRECTORY_SEPARATOR . $basePath;
        $duplicatedBasePathUnix = $basePath . '/' . $basePath;

        if (str_contains($localePath, $duplicatedBasePath) || str_contains($localePath, $duplicatedBasePathUnix)) {
            // Fix the duplicated path
            $localePath = str_replace($duplicatedBasePath, $basePath, $localePath);
            $localePath = str_replace($duplicatedBasePathUnix, $basePath, $localePath);
        }

        return $localePath;
    }
}
