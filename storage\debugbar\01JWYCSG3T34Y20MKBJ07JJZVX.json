{"__meta": {"id": "01JWYCSG3T34Y20MKBJ07JJZVX", "datetime": "2025-06-04 21:15:41", "utime": **********.050887, "method": "PUT", "uri": "/admin/plugins/status?name=gone-guard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749071736.651344, "end": **********.0509, "duration": 4.399555921554565, "duration_str": "4.4s", "measures": [{"label": "Booting", "start": 1749071736.651344, "relative_start": 0, "end": **********.403972, "relative_end": **********.403972, "duration": 0.****************, "duration_str": "753ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.404002, "relative_start": 0.****************, "end": **********.050901, "relative_end": 9.5367431640625e-07, "duration": 3.****************, "duration_str": "3.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.420825, "relative_start": 0.****************, "end": **********.434296, "relative_end": **********.434296, "duration": 0.013470888137817383, "duration_str": "13.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.046623, "relative_start": 4.****************, "end": **********.048294, "relative_end": **********.048294, "duration": 0.0016710758209228516, "duration_str": "1.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 709, "nb_visible_statements": 500, "nb_excluded_statements": 209, "nb_failed_statements": 0, "accumulated_duration": 2.006009999999999, "accumulated_duration_str": "2.01s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.443366, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.022}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 17, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 18, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.7830338, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "xmetr", "explain": null, "start_percent": 0.022, "width_percent": 0.081}, {"sql": "update `settings` set `value` = '6eadd395dd057399bda782a98b2db2e1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["6eadd395dd057399bda782a98b2db2e1", "2025-06-04 21:15:37", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.788927, "duration": 0.00801, "duration_str": "8.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 0.103, "width_percent": 0.399}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:37", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.798628, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 0.502, "width_percent": 0.208}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"blog\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"faq\\\",\\\"location\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"rss-feed\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"testimonial\\\",\\\"translation\\\",\\\"magic\\\",\\\"notification-plus\\\",\\\"real-estate\\\"]', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"blog\",\"captcha\",\"contact\",\"cookie-consent\",\"faq\",\"location\",\"newsletter\",\"payment\",\"paypal\",\"paystack\",\"razorpay\",\"rss-feed\",\"social-login\",\"sslcommerz\",\"stripe\",\"testimonial\",\"translation\",\"magic\",\"notification-plus\",\"real-estate\"]", "2025-06-04 21:15:37", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.804692, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 0.71, "width_percent": 0.261}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:37", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8112268, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 0.971, "width_percent": 0.204}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'enable_recaptcha_xmetr_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "enable_recaptcha_xmetr_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.816858, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 1.175, "width_percent": 0.228}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "enable_recaptcha_xmetr_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8225858, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 1.403, "width_percent": 0.222}, {"sql": "update `settings` set `value` = '[\\\"email\\\"]', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'real_estate_mandatory_fields_at_consult_form'", "type": "query", "params": [], "bindings": ["[\"email\"]", "2025-06-04 21:15:37", "real_estate_mandatory_fields_at_consult_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.82862, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 1.625, "width_percent": 0.209}, {"sql": "update `settings` set `value` = 'xmetr', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["xmetr", "2025-06-04 21:15:37", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.834714, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 1.833, "width_percent": 0.201}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.839865, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.035, "width_percent": 0.23}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:37", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.845642, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.265, "width_percent": 0.19}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-06-04 21:15:37", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.850626, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.455, "width_percent": 0.245}, {"sql": "update `settings` set `value` = 'all', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["all", "2025-06-04 21:15:37", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8567321, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.7, "width_percent": 0.217}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-06-04 21:15:37", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.862236, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 2.917, "width_percent": 0.204}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'permalink-xmetr-blog-models-post'", "type": "query", "params": [], "bindings": ["news", "2025-06-04 21:15:37", "permalink-xmetr-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8674328, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.121, "width_percent": 0.231}, {"sql": "update `settings` set `value` = 'news', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'permalink-xmetr-blog-models-category'", "type": "query", "params": [], "bindings": ["news", "2025-06-04 21:15:37", "permalink-xmetr-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.873214, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.352, "width_percent": 0.224}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.878854, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.576, "width_percent": 0.197}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-06-04 21:15:37", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.883869, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.774, "width_percent": 0.21}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.889207, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 3.984, "width_percent": 0.213}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-06-04 21:15:37", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.89463, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.197, "width_percent": 0.177}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-06-04 21:15:37", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.8993, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.375, "width_percent": 0.243}, {"sql": "update `settings` set `value` = 'XMetr – долгосрочная аренда недвижимости в Аргентине', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-site_title'", "type": "query", "params": [], "bindings": ["XMetr – долгосрочная аренда недвижимости в Аргентине", "2025-06-04 21:15:37", "theme-homzen-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.905371, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.618, "width_percent": 0.195}, {"sql": "update `settings` set `value` = 'Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-seo_description'", "type": "query", "params": [], "bindings": ["Долгосрочная аренда квартир в Аргентине и Буэнос-Айресе.", "2025-06-04 21:15:37", "theme-homzen-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.91041, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.813, "width_percent": 0.178}, {"sql": "update `settings` set `value` = '©XMetr - все права защищены.', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-copyright'", "type": "query", "params": [], "bindings": ["©XMetr - все права защищены.", "2025-06-04 21:15:37", "theme-homzen-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.915034, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 4.992, "width_percent": 0.182}, {"sql": "update `settings` set `value` = 'general/favicon-1.png', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-favicon'", "type": "query", "params": [], "bindings": ["general/favicon-1.png", "2025-06-04 21:15:37", "theme-homzen-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.919758, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.174, "width_percent": 0.219}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-logo'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-06-04 21:15:37", "theme-homzen-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9253392, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.393, "width_percent": 0.258}, {"sql": "update `settings` set `value` = 'general/logo-light.png', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-logo_light'", "type": "query", "params": [], "bindings": ["general/logo-light.png", "2025-06-04 21:15:37", "theme-homzen-logo_light"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.931575, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.652, "width_percent": 0.201}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-preloader_enabled'", "type": "query", "params": [], "bindings": ["no", "2025-06-04 21:15:37", "theme-homzen-preloader_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9367888, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 5.852, "width_percent": 0.231}, {"sql": "update `settings` set `value` = 'v2', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-preloader_version'", "type": "query", "params": [], "bindings": ["v2", "2025-06-04 21:15:37", "theme-homzen-preloader_version"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.942705, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.084, "width_percent": 0.203}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"name\\\",\\\"value\\\":\\\"Telegram\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-telegram\\\"},{\\\"key\\\":\\\"url\\\",\\\"value\\\":\\\"https:\\\\/\\\\/t.me\\\\/xmetrcom\\\"},{\\\"key\\\":\\\"image\\\",\\\"value\\\":null},{\\\"key\\\":\\\"color\\\",\\\"value\\\":\\\"transparent\\\"},{\\\"key\\\":\\\"background-color\\\",\\\"value\\\":\\\"transparent\\\"}]]', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-social_links'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"name\",\"value\":\"Telegram\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-telegram\"},{\"key\":\"url\",\"value\":\"https:\\/\\/t.me\\/xmetrcom\"},{\"key\":\"image\",\"value\":null},{\"key\":\"color\",\"value\":\"transparent\"},{\"key\":\"background-color\",\"value\":\"transparent\"}]]", "2025-06-04 21:15:37", "theme-homzen-social_links"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.948172, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.287, "width_percent": 0.181}, {"sql": "update `settings` set `value` = '[[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"facebook\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-facebook\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"x\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-x\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"pinterest\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-pinterest\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"linkedin\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-linkedin\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"whatsapp\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-brand-whatsapp\\\"}],[{\\\"key\\\":\\\"social\\\",\\\"value\\\":\\\"email\\\"},{\\\"key\\\":\\\"icon\\\",\\\"value\\\":\\\"ti ti-mail\\\"}]]', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-social_sharing'", "type": "query", "params": [], "bindings": ["[[{\"key\":\"social\",\"value\":\"facebook\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-facebook\"}],[{\"key\":\"social\",\"value\":\"x\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-x\"}],[{\"key\":\"social\",\"value\":\"pinterest\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-pinterest\"}],[{\"key\":\"social\",\"value\":\"linkedin\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-linkedin\"}],[{\"key\":\"social\",\"value\":\"whatsapp\"},{\"key\":\"icon\",\"value\":\"ti ti-brand-whatsapp\"}],[{\"key\":\"social\",\"value\":\"email\"},{\"key\":\"icon\",\"value\":\"ti ti-mail\"}]]", "2025-06-04 21:15:37", "theme-homzen-social_sharing"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.953124, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.468, "width_percent": 0.182}, {"sql": "update `settings` set `value` = 'rgb(103, 28, 201)', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-primary_color'", "type": "query", "params": [], "bindings": ["rgb(103, 28, 201)", "2025-06-04 21:15:37", "theme-homzen-primary_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.957886, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.65, "width_percent": 0.228}, {"sql": "update `settings` set `value` = 'rgb(101, 28, 197)', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-hover_color'", "type": "query", "params": [], "bindings": ["rgb(101, 28, 197)", "2025-06-04 21:15:37", "theme-homzen-hover_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.963437, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 6.878, "width_percent": 0.2}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-footer_background_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-04 21:15:37", "theme-homzen-footer_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.968377, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.078, "width_percent": 0.189}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-footer_background_image'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:37", "theme-homzen-footer_background_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.973351, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.267, "width_percent": 0.2}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-use_modal_for_authentication'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "theme-homzen-use_modal_for_authentication"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.9783852, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.468, "width_percent": 0.197}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:37", "theme-homzen-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.983331, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.665, "width_percent": 0.199}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-06-04 21:15:37", "theme-homzen-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.988512, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 7.864, "width_percent": 0.209}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-hotline'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:37", "theme-homzen-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.993827, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.074, "width_percent": 0.21}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-04 21:15:37' where `key` = 'theme-homzen-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-04 21:15:37", "theme-homzen-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": **********.999314, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.284, "width_percent": 0.202}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-breadcrumb_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-06-04 21:15:38", "theme-homzen-breadcrumb_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.004633, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.486, "width_percent": 0.229}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-breadcrumb_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-04 21:15:38", "theme-homzen-breadcrumb_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0104408, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.715, "width_percent": 0.195}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-lazy_load_images'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "theme-homzen-lazy_load_images"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.015485, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 8.91, "width_percent": 0.253}, {"sql": "update `settings` set `value` = 'general/placeholder.png', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-lazy_load_placeholder_image'", "type": "query", "params": [], "bindings": ["general/placeholder.png", "2025-06-04 21:15:38", "theme-homzen-lazy_load_placeholder_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.021846, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.163, "width_percent": 0.233}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-newsletter_popup_enable'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "theme-homzen-newsletter_popup_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0277328, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.396, "width_percent": 0.179}, {"sql": "update `settings` set `value` = 'general/newsletter-image.jpg', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-newsletter_popup_image'", "type": "query", "params": [], "bindings": ["general/newsletter-image.jpg", "2025-06-04 21:15:38", "theme-homzen-newsletter_popup_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0323682, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.575, "width_percent": 0.19}, {"sql": "update `settings` set `value` = 'Let’s join our newsletter!', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-newsletter_popup_title'", "type": "query", "params": [], "bindings": ["Let’s join our newsletter!", "2025-06-04 21:15:38", "theme-homzen-newsletter_popup_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.037864, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.765, "width_percent": 0.196}, {"sql": "update `settings` set `value` = 'Weekly Updates', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-newsletter_popup_subtitle'", "type": "query", "params": [], "bindings": ["Weekly Updates", "2025-06-04 21:15:38", "theme-homzen-newsletter_popup_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.043021, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 9.962, "width_percent": 0.191}, {"sql": "update `settings` set `value` = 'Do not worry we don’t spam!', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-newsletter_popup_description'", "type": "query", "params": [], "bindings": ["Do not worry we don’t spam!", "2025-06-04 21:15:38", "theme-homzen-newsletter_popup_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0480838, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.152, "width_percent": 0.214}, {"sql": "update `settings` set `value` = '14', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-properties_list_page_id'", "type": "query", "params": [], "bindings": ["14", "2025-06-04 21:15:38", "theme-homzen-properties_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.053862, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.367, "width_percent": 0.225}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-projects_list_page_id'", "type": "query", "params": [], "bindings": ["15", "2025-06-04 21:15:38", "theme-homzen-projects_list_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.059615, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.592, "width_percent": 0.228}, {"sql": "update `settings` set `value` = '2024-10-09 15:29:15', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'membership_authorization_at'", "type": "query", "params": [], "bindings": ["2024-10-09 15:29:15", "2025-06-04 21:15:38", "membership_authorization_at"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.06546, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 10.82, "width_percent": 0.21}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'is_completed_get_started'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:38", "is_completed_get_started"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.071086, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.03, "width_percent": 0.204}, {"sql": "update `settings` set `value` = 'no', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-enabled_back_to_top'", "type": "query", "params": [], "bindings": ["no", "2025-06-04 21:15:38", "theme-homzen-enabled_back_to_top"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.076361, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.235, "width_percent": 0.213}, {"sql": "update `settings` set `value` = 'M d, Y', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-date_format'", "type": "query", "params": [], "bindings": ["M d, Y", "2025-06-04 21:15:38", "theme-homzen-date_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.081893, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.448, "width_percent": 0.207}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-show_site_name'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "theme-homzen-show_site_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0878599, "duration": 0.0043, "duration_str": "4.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.654, "width_percent": 0.214}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-seo_title'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:38", "theme-homzen-seo_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.093548, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 11.869, "width_percent": 0.207}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-seo_index'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:38", "theme-homzen-seo_index"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.0987809, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.076, "width_percent": 0.176}, {"sql": "update `settings` set `value` = 'general/header-logo2.png', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-seo_og_image'", "type": "query", "params": [], "bindings": ["general/header-logo2.png", "2025-06-04 21:15:38", "theme-homzen-seo_og_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.1036458, "duration": 0.009720000000000001, "duration_str": "9.72ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.252, "width_percent": 0.485}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-term_and_privacy_policy_url'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:38", "theme-homzen-term_and_privacy_policy_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.114553, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.737, "width_percent": 0.199}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_primary_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-06-04 21:15:38", "theme-homzen-tp_primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.119657, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 12.936, "width_percent": 0.197}, {"sql": "update `settings` set `value` = 'Roboto', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_heading_font'", "type": "query", "params": [], "bindings": ["Roboto", "2025-06-04 21:15:38", "theme-homzen-tp_heading_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.124698, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.133, "width_percent": 0.202}, {"sql": "update `settings` set `value` = '80', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h1_size'", "type": "query", "params": [], "bindings": ["80", "2025-06-04 21:15:38", "theme-homzen-tp_h1_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.129969, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.335, "width_percent": 0.202}, {"sql": "update `settings` set `value` = '55', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h2_size'", "type": "query", "params": [], "bindings": ["55", "2025-06-04 21:15:38", "theme-homzen-tp_h2_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.135259, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.538, "width_percent": 0.196}, {"sql": "update `settings` set `value` = '40', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h3_size'", "type": "query", "params": [], "bindings": ["40", "2025-06-04 21:15:38", "theme-homzen-tp_h3_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.1403482, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.734, "width_percent": 0.215}, {"sql": "update `settings` set `value` = '35', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h4_size'", "type": "query", "params": [], "bindings": ["35", "2025-06-04 21:15:38", "theme-homzen-tp_h4_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.145899, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 13.95, "width_percent": 0.207}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h5_size'", "type": "query", "params": [], "bindings": ["30", "2025-06-04 21:15:38", "theme-homzen-tp_h5_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.1512911, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.157, "width_percent": 0.197}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_h6_size'", "type": "query", "params": [], "bindings": ["20", "2025-06-04 21:15:38", "theme-homzen-tp_h6_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.156378, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.354, "width_percent": 0.195}, {"sql": "update `settings` set `value` = '15', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-tp_body_size'", "type": "query", "params": [], "bindings": ["15", "2025-06-04 21:15:38", "theme-homzen-tp_body_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.161578, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.55, "width_percent": 0.201}, {"sql": "update `settings` set `value` = '20', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-number_of_projects_per_page'", "type": "query", "params": [], "bindings": ["20", "2025-06-04 21:15:38", "theme-homzen-number_of_projects_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.166877, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.751, "width_percent": 0.208}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-number_of_properties_per_page'", "type": "query", "params": [], "bindings": ["30", "2025-06-04 21:15:38", "theme-homzen-number_of_properties_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.172462, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 14.959, "width_percent": 0.207}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-number_of_related_projects'", "type": "query", "params": [], "bindings": ["8", "2025-06-04 21:15:38", "theme-homzen-number_of_related_projects"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.177881, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.166, "width_percent": 0.206}, {"sql": "update `settings` set `value` = '8', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-number_of_related_properties'", "type": "query", "params": [], "bindings": ["8", "2025-06-04 21:15:38", "theme-homzen-number_of_related_properties"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.183219, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.372, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '43.615134, -76.393186', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-latitude_longitude_center_on_properties_page'", "type": "query", "params": [], "bindings": ["43.615134, -76.393186", "2025-06-04 21:15:38", "theme-homzen-latitude_longitude_center_on_properties_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.188347, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.565, "width_percent": 0.243}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-real_estate_property_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-06-04 21:15:38", "theme-homzen-real_estate_property_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.194485, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 15.808, "width_percent": 0.212}, {"sql": "update `settings` set `value` = '3', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-real_estate_property_detail_layout'", "type": "query", "params": [], "bindings": ["3", "2025-06-04 21:15:38", "theme-homzen-real_estate_property_detail_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.199952, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.021, "width_percent": 0.197}, {"sql": "update `settings` set `value` = 'top-map', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-real_estate_project_listing_layout'", "type": "query", "params": [], "bindings": ["top-map", "2025-06-04 21:15:38", "theme-homzen-real_estate_project_listing_layout"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2061648, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.218, "width_percent": 0.195}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-real_estate_show_map_on_single_detail_page'", "type": "query", "params": [], "bindings": ["yes", "2025-06-04 21:15:38", "theme-homzen-real_estate_show_map_on_single_detail_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.211411, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.413, "width_percent": 0.24}, {"sql": "update `settings` set `value` = 'loichenko', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'licensed_to'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "2025-06-04 21:15:38", "licensed_to"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.218226, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.653, "width_percent": 0.202}, {"sql": "update `settings` set `value` = '[\\\"<EMAIL>\\\"]', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'admin_email'", "type": "query", "params": [], "bindings": ["[\"<EMAIL>\"]", "2025-06-04 21:15:38", "admin_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.223418, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 16.855, "width_percent": 0.203}, {"sql": "update `settings` set `value` = 'UTC', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'time_zone'", "type": "query", "params": [], "bindings": ["UTC", "2025-06-04 21:15:38", "time_zone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2287428, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.058, "width_percent": 0.194}, {"sql": "update `settings` set `value` = 'ltr', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'locale_direction'", "type": "query", "params": [], "bindings": ["ltr", "2025-06-04 21:15:38", "locale_direction"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.234135, "duration": 0.00899, "duration_str": "8.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.253, "width_percent": 0.448}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'enable_send_error_reporting_via_email'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "enable_send_error_reporting_via_email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.244437, "duration": 0.00845, "duration_str": "8.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 17.701, "width_percent": 0.421}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'redirect_404_to_homepage'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "redirect_404_to_homepage"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.254533, "duration": 0.0046, "duration_str": "4.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.122, "width_percent": 0.229}, {"sql": "update `settings` set `value` = '30', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'audit_log_data_retention_period'", "type": "query", "params": [], "bindings": ["30", "2025-06-04 21:15:38", "audit_log_data_retention_period"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.260224, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.351, "width_percent": 0.208}, {"sql": "update `settings` set `value` = 'en', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'locale'", "type": "query", "params": [], "bindings": ["en", "2025-06-04 21:15:38", "locale"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2654781, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.559, "width_percent": 0.198}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'language_show_default_item_if_current_version_not_existed'", "type": "query", "params": [], "bindings": ["1", "2025-06-04 21:15:38", "language_show_default_item_if_current_version_not_existed"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2713199, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.757, "width_percent": 0.212}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'language_auto_detect_user_language'", "type": "query", "params": [], "bindings": ["0", "2025-06-04 21:15:38", "language_auto_detect_user_language"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2766879, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 18.969, "width_percent": 0.221}, {"sql": "update `settings` set `value` = '#f7f7f7', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-top_header_background_color'", "type": "query", "params": [], "bindings": ["#f7f7f7", "2025-06-04 21:15:38", "theme-homzen-top_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.28218, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.191, "width_percent": 0.184}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-top_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-04 21:15:38", "theme-homzen-top_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.287109, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.375, "width_percent": 0.195}, {"sql": "update `settings` set `value` = '#ffffff', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-main_header_background_color'", "type": "query", "params": [], "bindings": ["#ffffff", "2025-06-04 21:15:38", "theme-homzen-main_header_background_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2928069, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.57, "width_percent": 0.205}, {"sql": "update `settings` set `value` = '#161e2d', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-main_header_text_color'", "type": "query", "params": [], "bindings": ["#161e2d", "2025-06-04 21:15:38", "theme-homzen-main_header_text_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.2979732, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.776, "width_percent": 0.188}, {"sql": "update `settings` set `value` = '#e4e4e4', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-main_header_border_color'", "type": "query", "params": [], "bindings": ["#e4e4e4", "2025-06-04 21:15:38", "theme-homzen-main_header_border_color"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.302775, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 19.964, "width_percent": 0.204}, {"sql": "update `settings` set `value` = 'yes', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_enable'", "type": "query", "params": [], "bindings": ["yes", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_enable"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.3087249, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.168, "width_percent": 0.211}, {"sql": "update `settings` set `value` = 'minimal', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_style'", "type": "query", "params": [], "bindings": ["minimal", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_style"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.314031, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.379, "width_percent": 0.193}, {"sql": "update `settings` set `value` = '? Мы используем cookies, что бы сделать сайт лучше.', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_message'", "type": "query", "params": [], "bindings": ["? Мы используем cookies, что бы сделать сайт лучше.", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.318902, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.573, "width_percent": 0.217}, {"sql": "update `settings` set `value` = 'Ок', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_button_text'", "type": "query", "params": [], "bindings": ["Ок", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_button_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.3243861, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.79, "width_percent": 0.201}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.3296201, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 20.99, "width_percent": 0.19}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-04 21:15:38' where `key` = 'theme-homzen-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["", "2025-06-04 21:15:38", "theme-homzen-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 372}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 335}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 110}], "start": 1749071738.334708, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "xmetr", "explain": null, "start_percent": 21.181, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.33994, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 21.376, "width_percent": 0.209}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.344519, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 21.585, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.348471, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 21.771, "width_percent": 0.17}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.3521378, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 21.941, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.356421, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.137, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.3607051, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.331, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.365211, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.538, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.369568, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.738, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.374307, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 22.957, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.378914, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.169, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.383457, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.377, "width_percent": 0.22}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.3882198, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.598, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.392498, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.794, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.396727, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 23.988, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.401062, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 24.186, "width_percent": 0.224}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.405871, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 24.41, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.409918, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 24.595, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.414133, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 24.788, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.418304, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 24.977, "width_percent": 0.229}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.423248, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.206, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.427953, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.423, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.4326918, "duration": 0.01061, "duration_str": "10.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 25.641, "width_percent": 0.529}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.4436562, "duration": 0.015550000000000001, "duration_str": "15.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.17, "width_percent": 0.775}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.459525, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 26.946, "width_percent": 0.226}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.464303, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.172, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.468369, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.361, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.472872, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.567, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.477029, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.757, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.4811869, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 27.946, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.485194, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.131, "width_percent": 0.21}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.4897368, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.341, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.4937892, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.528, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.497495, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.7, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5013041, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 28.876, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.505315, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 29.056, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.509517, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 29.248, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5135581, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 29.432, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5179548, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 29.631, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.522069, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 29.818, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5264678, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.02, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5307238, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.216, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.534755, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.404, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5390089, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.597, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5436032, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.808, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5477269, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 30.997, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.552047, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.194, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5562232, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.379, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.560123, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.56, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.564054, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.745, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.568003, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 31.929, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.572088, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.116, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.5763001, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.309, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.580424, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.497, "width_percent": 0.224}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.585271, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.722, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.589308, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 32.906, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.593565, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.101, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.597635, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.286, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.601909, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.48, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.605981, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.666, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.609965, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 33.85, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.613705, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.025, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.617445, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.199, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.621358, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.371, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.625439, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.559, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.629394, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.745, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.633283, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 34.925, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.6371531, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.101, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.6413348, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.292, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.645454, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.48, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.649743, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.674, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.653927, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 35.865, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.658275, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.064, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.662546, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.26, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.666564, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.442, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.670797, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.636, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.6753101, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 36.843, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.679466, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.033, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.683706, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.225, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.687894, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.417, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.692031, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.606, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.6960928, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.791, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.700348, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 37.986, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.7043889, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.17, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.708677, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.366, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.71283, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.555, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.716933, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.744, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.721093, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 38.935, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.72535, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.129, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.729507, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.316, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.7338152, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.512, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.738039, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.701, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.742312, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 39.896, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.747002, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.112, "width_percent": 0.202}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.751442, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.315, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.755525, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.502, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.759731, "duration": 0.00731, "duration_str": "7.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 40.694, "width_percent": 0.364}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.767343, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.059, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.7713, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.242, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.775295, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.424, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.779354, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.612, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.783256, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.796, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.787047, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 41.973, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.7909448, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.15, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.795007, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.338, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.798722, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.512, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.80255, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.692, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8065321, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 42.872, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.810977, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.061, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.815335, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.251, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8198018, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.445, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8239398, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.633, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8279352, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.82, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8315642, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 43.991, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8352308, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.163, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.839301, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.35, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8432002, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.53, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.847012, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.71, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8507602, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 44.886, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.854661, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.065, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.858693, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.252, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.862514, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.429, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8662372, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.603, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.86998, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.776, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.874063, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 45.957, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.878132, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.144, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.882166, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.334, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.885956, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.511, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.8898869, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.691, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.893683, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 46.867, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.89749, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.046, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.90145, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.226, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9056802, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.409, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9097888, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.598, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.913578, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.776, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9173949, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 47.953, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9212759, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.129, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.925288, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.315, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.929411, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.509, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.933212, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.684, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.93733, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 48.877, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.941267, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.058, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9450629, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.236, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9489222, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.415, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.952619, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.587, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.956572, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.768, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.960559, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 49.953, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9651492, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.165, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.969592, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.372, "width_percent": 0.225}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.974483, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.597, "width_percent": 0.209}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.978943, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.806, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.9828262, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 50.988, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.986687, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.168, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.990553, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.345, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.994524, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.53, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071738.998234, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.703, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0020669, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 51.88, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0061831, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.066, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.010269, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.255, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.014048, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.431, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0181289, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.622, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0220091, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.798, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.026274, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 52.99, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0305321, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.185, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.034823, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.378, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.039291, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.579, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.043589, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.776, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.047722, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 53.964, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.052104, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.164, "width_percent": 0.214}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0567422, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.378, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.0610912, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.576, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.06525, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.765, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.069448, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 54.957, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.073693, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.15, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.077859, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.341, "width_percent": 0.2}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.082227, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.542, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.086545, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.74, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.090887, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 55.938, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.095063, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.127, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.099121, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.312, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1038442, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.528, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1083589, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.728, "width_percent": 0.203}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.112807, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 56.931, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.116968, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.121, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.121273, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.314, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1255052, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.507, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.12944, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.687, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1334078, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 57.868, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.137718, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.065, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.142002, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.261, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1461601, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.451, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.150552, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.652, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.15446, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 58.831, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.158572, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.019, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.162825, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.212, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1671011, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.401, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1716099, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.599, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1759071, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.796, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1802301, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 59.994, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.1844199, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.185, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.188668, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.373, "width_percent": 0.204}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.193091, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.576, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.197207, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.763, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.201863, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 60.97, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.206265, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.163, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2106302, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.362, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.215106, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.568, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.219259, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.758, "width_percent": 0.202}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2236428, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 61.96, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.227934, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.155, "width_percent": 0.204}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2323742, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.359, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.236534, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.549, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.240746, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.74, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.244929, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 62.929, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.249249, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.127, "width_percent": 0.203}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2536688, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.33, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.25777, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.518, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2619529, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.711, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.265864, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 63.895, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.269691, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.072, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.273929, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.267, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2780602, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.457, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.282113, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.646, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.286174, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 64.836, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.290756, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.048, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.294861, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.237, "width_percent": 0.173}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.2985551, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.411, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.302461, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.591, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.306668, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.785, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.310755, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 65.977, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.314553, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.156, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.318577, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.342, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.323007, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.544, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.326946, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.726, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.33066, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 66.901, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.334539, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.08, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.3384511, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.259, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.342963, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.467, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.34729, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.665, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.351542, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 67.858, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.355921, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.053, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.3601098, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.244, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.364585, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.45, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.368791, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.642, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.373262, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 68.848, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.377531, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.044, "width_percent": 0.202}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.38193, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.245, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.386257, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.444, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.390428, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.636, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.394367, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 69.819, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.398504, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.008, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4028919, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.209, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4070852, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.401, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4112961, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.6, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.415519, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.798, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4195268, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 70.98, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.423827, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.177, "width_percent": 0.2}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.428182, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.377, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.43265, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.582, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4370348, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.781, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4411309, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 71.968, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.445436, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.165, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.449594, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.357, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.453711, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.549, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.457795, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.736, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.461802, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 72.923, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.465627, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.103, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4698298, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.299, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.473959, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.487, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.4782588, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.686, "width_percent": 0.246}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.483428, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 73.932, "width_percent": 0.2}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.487696, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.132, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.491947, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.327, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.496167, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.523, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5004349, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.713, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.505268, "duration": 0.00505, "duration_str": "5.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 74.926, "width_percent": 0.252}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.511167, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.178, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.516174, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.397, "width_percent": 0.224}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5212681, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.621, "width_percent": 0.218}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.526228, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 75.839, "width_percent": 0.225}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.53135, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.064, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5362089, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.275, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.541038, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.487, "width_percent": 0.203}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5456939, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.69, "width_percent": 0.229}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.550932, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 76.919, "width_percent": 0.213}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5561, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.132, "width_percent": 0.246}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.5616722, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.378, "width_percent": 0.232}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.566929, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.611, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.571915, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 77.827, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.57721, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.046, "width_percent": 0.205}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.581907, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.251, "width_percent": 0.213}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.586816, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.464, "width_percent": 0.216}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.591734, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.68, "width_percent": 0.229}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.59692, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 78.908, "width_percent": 0.212}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.601776, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.12, "width_percent": 0.231}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6070158, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.352, "width_percent": 0.258}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.612778, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.609, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.617633, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 79.82, "width_percent": 0.224}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.622839, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.044, "width_percent": 0.23}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.628084, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.274, "width_percent": 0.23}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6332922, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.504, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6380239, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.71, "width_percent": 0.221}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.643044, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 80.931, "width_percent": 0.22}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.648002, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.152, "width_percent": 0.204}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.65273, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.356, "width_percent": 0.211}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.657573, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.567, "width_percent": 0.22}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.662525, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 81.787, "width_percent": 0.214}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6673548, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.001, "width_percent": 0.204}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.672027, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.205, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.67663, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.406, "width_percent": 0.206}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.681281, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.612, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6861, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 82.81, "width_percent": 0.231}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6913579, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.041, "width_percent": 0.204}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.6961071, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.245, "width_percent": 0.243}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.701862, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.489, "width_percent": 0.224}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.706959, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.713, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.711262, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 83.908, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.715383, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.095, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.719755, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.294, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.723858, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.482, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7277532, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.661, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.731617, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 84.842, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.735539, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.024, "width_percent": 0.181}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7396088, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.205, "width_percent": 0.197}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.743928, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.402, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.748013, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.587, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.752456, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.786, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7566, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 85.972, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.760741, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.16, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7649698, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.352, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.769093, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.539, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.77347, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.738, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.777695, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 86.93, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7819922, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.126, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.786143, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.314, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.790417, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.507, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.794661, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.701, "width_percent": 0.177}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.7985952, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 87.878, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.802991, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.076, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8072362, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.264, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.811413, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.454, "width_percent": 0.188}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8155649, "duration": 0.0047599999999999995, "duration_str": "4.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.642, "width_percent": 0.237}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.820702, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 88.879, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8247888, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.064, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.829062, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.261, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.83292, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.441, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.836654, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.613, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.840607, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.792, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.844747, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 89.983, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.848447, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.156, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.852272, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.331, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.856345, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.506, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.860575, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.698, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8648648, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 90.893, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8692188, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.089, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8735108, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.275, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.8776271, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.462, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.88185, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.654, "width_percent": 0.189}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.886026, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 91.843, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.890281, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.035, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.894553, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.226, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.898763, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.418, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9029188, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.604, "width_percent": 0.196}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.907267, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.801, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.911463, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 92.991, "width_percent": 0.191}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.915666, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.182, "width_percent": 0.199}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.920033, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.381, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.924143, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.565, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.92837, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.759, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9325862, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 93.957, "width_percent": 0.171}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9363441, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.128, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9404209, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.308, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.944552, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.495, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9486969, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.689, "width_percent": 0.176}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9525042, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 94.865, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.956436, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.045, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.96071, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.239, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.964854, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.432, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.968895, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.619, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.97294, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.801, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.977233, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 95.994, "width_percent": 0.209}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.981801, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.203, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.986125, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.395, "width_percent": 0.194}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.990695, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.59, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.995037, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.785, "width_percent": 0.182}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071739.9989998, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 96.967, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.0028791, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.146, "width_percent": 0.172}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.006944, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.318, "width_percent": 0.195}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.011695, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.513, "width_percent": 0.201}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.016114, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.715, "width_percent": 0.184}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.020293, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 97.899, "width_percent": 0.193}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.024915, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.093, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.02925, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.291, "width_percent": 0.185}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.033299, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.476, "width_percent": 0.183}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.0373251, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.659, "width_percent": 0.19}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.041518, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 98.848, "width_percent": 0.186}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.0456102, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.034, "width_percent": 0.18}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.049541, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.214, "width_percent": 0.175}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.05354, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.39, "width_percent": 0.198}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.058172, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.588, "width_percent": 0.225}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749071740.063092, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "xmetr", "explain": null, "start_percent": 99.813, "width_percent": 0.187}, {"sql": "... 209 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/plugins/status?name=gone-guard", "action_name": "plugins.change.status", "controller_action": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update", "uri": "PUT admin/plugins/status", "permission": "plugins.index", "controller": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\PluginManagement\\Http\\Controllers", "prefix": "admin/plugins", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php:93-130</a>", "middleware": "web, core, auth, preventDemo", "duration": "4.41s", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1829999923 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">gone-guard</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829999923\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-935242058 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">put</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935242058\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1789833980 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik4wYTJuNUVMTGZKM0lhemphYUpSV0E9PSIsInZhbHVlIjoiQ2NhUTljbWJKVFQrZGVodTlaUFg4d2orMGdPTU1qbkM0ZlhoUkpRNWl5ZzZzTmh6VVUrREtaZUtPWS9pVmswRytmNEE5NWdQWkJDLy9ubVJOQUFSZGUzekYrL1FyTWtKdmJSb1BvaXVXNGVkN3dOTTBJUUNxSnlaUElxci9ubXAiLCJtYWMiOiI3YzMyMzE5N2IzZTUxNTFlZWY3NDg2YzMzYzJhNDZlMWIxYzhmYzE4NDJhNjRkYzNmMDc3YmI1ZmE2NTkxYTViIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">https://xmetr.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6IjcyOGQ5ODc2LWZiOGItNGRiOC1hZjU2LWRjZmYyZWY1ZWNmZCIsImMiOjE3NDkwNzA5NjUyNDAsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1749070966$o59$g1$t1749071719$j56$l0$h0; XSRF-TOKEN=eyJpdiI6Ik4wYTJuNUVMTGZKM0lhemphYUpSV0E9PSIsInZhbHVlIjoiQ2NhUTljbWJKVFQrZGVodTlaUFg4d2orMGdPTU1qbkM0ZlhoUkpRNWl5ZzZzTmh6VVUrREtaZUtPWS9pVmswRytmNEE5NWdQWkJDLy9ubVJOQUFSZGUzekYrL1FyTWtKdmJSb1BvaXVXNGVkN3dOTTBJUUNxSnlaUElxci9ubXAiLCJtYWMiOiI3YzMyMzE5N2IzZTUxNTFlZWY3NDg2YzMzYzJhNDZlMWIxYzhmYzE4NDJhNjRkYzNmMDc3YmI1ZmE2NTkxYTViIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InJQWE1kL0xUNnBBangrdkRtZWM5dHc9PSIsInZhbHVlIjoieUQ2ZGVrK1drQlpnSUEzMnN3b1V4MytLV2F5ZXZST1BTQjFwMEEwa3lRUG5rYzBRV1VXRERuZjYrOTExd3Vza25Jd0NZOWp6R0VndTVvUE1XcTR1c0J0MWRYTUdYT0F4bG1mc1EzN0ZlVkN6OVFBTkdCVTd4cXNHWFJEMkkyRysiLCJtYWMiOiI1NjZmYWY0NTUyZmU4M2NhMWFlOTA0MGMxNDdmODljZWM5ODUzNjdlMTUwMzgxNGU4Y2MyYTY4YmRjODc4N2Q5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789833980\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2062507137 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BAeSTZYHsbAurf11vjLNWg21xAsIW02xvuDgqkPV</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dWU426jwRXntMhhi9Tgot7HkGpY03jgX2flj8iIH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062507137\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-558829327 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 04 Jun 2025 21:15:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558829327\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-444274462 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BAeSTZYHsbAurf11vjLNWg21xAsIW02xvuDgqkPV</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">https://xmetr.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>previous_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ru</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444274462\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/plugins/status?name=gone-guard", "action_name": "plugins.change.status", "controller_action": "Xmetr\\PluginManagement\\Http\\Controllers\\PluginManagementController@update"}, "badge": null}}