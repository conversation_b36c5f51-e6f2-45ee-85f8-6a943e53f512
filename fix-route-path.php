<?php

// This script directly fixes the duplicated path issue in route cache files

// Get the bootstrap/cache directory
$cacheDir = __DIR__ . '/bootstrap/cache';

// Check for files with the pattern routes-v7_*.php
$files = glob($cacheDir . '/routes-v7_*.php');

// Get the current base path dynamically
$basePath = __DIR__;
$basePathWindows = str_replace('/', '\\', $basePath);
$basePathUnix = str_replace('\\', '/', $basePath);

foreach ($files as $file) {
    echo "Checking file: " . basename($file) . "\n";

    // Read the file content
    $content = file_get_contents($file);
    $originalContent = $content;

    // Fix duplicated Windows paths
    $duplicatedWindowsPath = $basePathWindows . '\\' . $basePathWindows . '\\';
    $content = str_replace($duplicatedWindowsPath, $basePathWindows . '\\', $content);

    // Fix duplicated Unix paths
    $duplicatedUnixPath = $basePathUnix . '/' . $basePathUnix . '/';
    $content = str_replace($duplicatedUnixPath, $basePathUnix . '/', $content);

    // Also fix the specific case mentioned in the error
    $content = str_replace(
        'D:\\laragon\\www\\xmetr\\D:\\laragon\\www\\xmetr\\',
        'D:\\laragon\\www\\xmetr\\',
        $content
    );

    // If changes were made, save the file
    if ($content !== $originalContent) {
        echo "Fixing duplicated path in: " . basename($file) . "\n";
        file_put_contents($file, $content);
    } else {
        echo "No duplicated paths found in: " . basename($file) . "\n";
    }
}

echo "Done!\n";
